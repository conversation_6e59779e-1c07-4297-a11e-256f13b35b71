SELECT
  D.event_time AS event_time,
  D.`timestamp` AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  <PERSON><PERSON>imei AS imei,
  V.id AS vehicle_id,
  V.mfr_org_id AS mfr_org_id,
  V.owner_org_id AS owner_org_id,
  CAST(NULL AS FLOAT) AS ai_temp,
  CAST(NULL AS FLOAT) AS ai_vin,
  CAST(NULL AS FLOAT) AS ai_vsys,
  CAST(NULL AS FLOAT) AS ai_vbuck,
  CAST(NULL AS INTEGER) AS ai_vusr1,
  CAST(NULL AS INTEGER) AS ai_vusr2,
  CAST(NULL AS BOOLEAN) AS di_usr1,
  CAST(NULL AS BOOLEAN) AS di_usr2,
  D.di_motion AS di_motion,
  CAST(NULL AS BOOLEAN) AS di_main_power,
  D.di_ignition AS di_ignition,
  CAST(NULL AS BOOLEAN) AS di_tamper,
  CAST(NULL AS BOOLEAN) AS do_usr1,
  CAST(NULL AS BOOLEAN) AS do_usr2,
  TRY_CAST(NULL AS FLOAT) AS gyro_x,
  TRY_CAST(NULL AS FLOAT) AS gyro_y,
  TRY_CAST(NULL AS FLOAT) AS gyro_z,
  TRY_CAST(D.accel_x AS FLOAT) AS accel_x,
  TRY_CAST(D.accel_y AS FLOAT) AS accel_y,
  TRY_CAST(D.accel_z AS FLOAT) AS accel_z,
  TRY_CAST(D.grv_x AS FLOAT) AS grv_x,
  TRY_CAST(D.grv_y AS FLOAT) AS grv_y,
  TRY_CAST(D.grv_z AS FLOAT) AS grv_z,
  D.ai_lean_angle AS ai_lean_angle
FROM
  imu_raw AS D
  INNER JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  V.operation_status = 'ACTIVE'
  AND D.`timestamp` BETWEEN EXTRACT(EPOCH FROM (NOW() - INTERVAL '5' DAY)) AND EXTRACT(EPOCH FROM (NOW() + INTERVAL '5' SECOND));